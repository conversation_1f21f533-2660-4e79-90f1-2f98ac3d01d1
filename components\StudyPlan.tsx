import { supabase } from "@/lib/supabase";
import { useUser } from "@clerk/clerk-expo";
import React, { useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  ScrollView,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from "react-native";

interface Milestone {
  week: number;
  title: string;
  description: string;
  topics: string[];
  estimatedHours: number;
}

interface MilestoneProgress {
  id: string;
  milestone_index: number;
  completed: boolean;
  completed_at: string | null;
  notes: string | null;
}

interface StudyPlan {
  id: string;
  title: string;
  description: string;
  milestones: Milestone[];
  start_date: string;
  target_completion_date: string;
  status: "active" | "completed" | "paused";
}

interface StudyPlanProps {
  studyPlan: StudyPlan;
  courseId: string;
  onProgressUpdate?: () => void;
}

export default function StudyPlanComponent({
  studyPlan,
  courseId,
  onProgressUpdate,
}: StudyPlanProps) {
  const { user } = useUser();
  const [milestoneProgress, setMilestoneProgress] = useState<
    MilestoneProgress[]
  >([]);
  const [editingNotes, setEditingNotes] = useState<number | null>(null);
  const [noteText, setNoteText] = useState("");
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadMilestoneProgress();
  }, [studyPlan.id]);

  const loadMilestoneProgress = async () => {
    try {
      const { data: supabaseUser } = await supabase
        .from("users")
        .select("id")
        .eq("clerk_user_id", user?.id)
        .single();

      if (!supabaseUser) return;

      const { data, error } = await supabase
        .from("milestone_progress")
        .select("*")
        .eq("study_plan_id", studyPlan.id)
        .eq("user_id", supabaseUser.id);

      if (error) {
        console.error("Error loading milestone progress:", error);
        return;
      }

      setMilestoneProgress(data || []);
    } catch (error) {
      console.error("Error loading milestone progress:", error);
    } finally {
      setLoading(false);
    }
  };

  const getMilestoneProgress = (
    milestoneIndex: number
  ): MilestoneProgress | null => {
    return (
      milestoneProgress.find((p) => p.milestone_index === milestoneIndex) ||
      null
    );
  };

  const toggleMilestoneCompletion = async (milestoneIndex: number) => {
    try {
      const { data: supabaseUser } = await supabase
        .from("users")
        .select("id")
        .eq("clerk_user_id", user?.id)
        .single();

      if (!supabaseUser) return;

      const existingProgress = getMilestoneProgress(milestoneIndex);
      const isCompleted = existingProgress?.completed || false;

      if (existingProgress) {
        // Update existing progress
        const { error } = await supabase
          .from("milestone_progress")
          .update({
            completed: !isCompleted,
            completed_at: !isCompleted ? new Date().toISOString() : null,
          })
          .eq("id", existingProgress.id);

        if (error) throw error;
      } else {
        // Create new progress entry
        const { error } = await supabase.from("milestone_progress").insert({
          study_plan_id: studyPlan.id,
          user_id: supabaseUser.id,
          milestone_index: milestoneIndex,
          completed: true,
          completed_at: new Date().toISOString(),
        });

        if (error) throw error;
      }

      await loadMilestoneProgress();
      onProgressUpdate?.();
    } catch (error) {
      console.error("Error updating milestone progress:", error);
      Alert.alert("Error", "Failed to update milestone progress");
    }
  };

  const saveNotes = async (milestoneIndex: number) => {
    try {
      const { data: supabaseUser } = await supabase
        .from("users")
        .select("id")
        .eq("clerk_user_id", user?.id)
        .single();

      if (!supabaseUser) return;

      const existingProgress = getMilestoneProgress(milestoneIndex);

      if (existingProgress) {
        // Update existing notes
        const { error } = await supabase
          .from("milestone_progress")
          .update({ notes: noteText })
          .eq("id", existingProgress.id);

        if (error) throw error;
      } else {
        // Create new progress entry with notes
        const { error } = await supabase.from("milestone_progress").insert({
          study_plan_id: studyPlan.id,
          user_id: supabaseUser.id,
          milestone_index: milestoneIndex,
          completed: false,
          notes: noteText,
        });

        if (error) throw error;
      }

      await loadMilestoneProgress();
      setEditingNotes(null);
      setNoteText("");
    } catch (error) {
      console.error("Error saving notes:", error);
      Alert.alert("Error", "Failed to save notes");
    }
  };

  const getCompletionPercentage = () => {
    const totalMilestones = studyPlan.milestones.length;
    const completedMilestones = milestoneProgress.filter(
      (p) => p.completed
    ).length;
    return totalMilestones > 0
      ? Math.round((completedMilestones / totalMilestones) * 100)
      : 0;
  };

  const getWeeksSinceStart = () => {
    const startDate = new Date(studyPlan.start_date);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - startDate.getTime());
    const diffWeeks = Math.ceil(diffTime / (1000 * 60 * 60 * 24 * 7));
    return diffWeeks;
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <Text style={styles.loadingText}>Loading study plan...</Text>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>{studyPlan.title}</Text>
        <Text style={styles.description}>{studyPlan.description}</Text>

        <View style={styles.progressContainer}>
          <View style={styles.progressBar}>
            <View
              style={[
                styles.progressFill,
                { width: `${getCompletionPercentage()}%` },
              ]}
            />
          </View>
          <Text style={styles.progressText}>
            {getCompletionPercentage()}% Complete (
            {milestoneProgress.filter((p) => p.completed).length}/
            {studyPlan.milestones.length} milestones)
          </Text>
        </View>

        <View style={styles.dateInfo}>
          <Text style={styles.dateText}>
            Started: {new Date(studyPlan.start_date).toLocaleDateString()}
          </Text>
          <Text style={styles.dateText}>
            Target:{" "}
            {new Date(studyPlan.target_completion_date).toLocaleDateString()}
          </Text>
          <Text style={styles.dateText}>
            Week {getWeeksSinceStart()} of {studyPlan.milestones.length}
          </Text>
        </View>
      </View>

      <View style={styles.milestonesContainer}>
        <Text style={styles.sectionTitle}>Study Milestones</Text>
        {studyPlan.milestones.map((milestone, index) => {
          const progress = getMilestoneProgress(index);
          const isCompleted = progress?.completed || false;
          const isCurrentWeek = index + 1 === getWeeksSinceStart();

          return (
            <View
              key={index}
              style={[
                styles.milestoneCard,
                isCompleted && styles.completedMilestone,
                isCurrentWeek && styles.currentMilestone,
              ]}
            >
              <View style={styles.milestoneHeader}>
                <TouchableOpacity
                  style={[styles.checkbox, isCompleted && styles.checkedBox]}
                  onPress={() => toggleMilestoneCompletion(index)}
                >
                  {isCompleted && <Text style={styles.checkmark}>✓</Text>}
                </TouchableOpacity>

                <View style={styles.milestoneInfo}>
                  <Text
                    style={[
                      styles.milestoneTitle,
                      isCompleted && styles.completedText,
                    ]}
                  >
                    Week {milestone.week}: {milestone.title}
                  </Text>
                  <Text style={styles.milestoneDescription}>
                    {milestone.description}
                  </Text>
                  <Text style={styles.estimatedHours}>
                    Estimated: {milestone.estimatedHours} hours
                  </Text>
                </View>
              </View>

              {milestone.topics.length > 0 && (
                <View style={styles.topicsContainer}>
                  <Text style={styles.topicsTitle}>Topics:</Text>
                  {milestone.topics.map((topic, topicIndex) => (
                    <Text key={topicIndex} style={styles.topic}>
                      • {topic}
                    </Text>
                  ))}
                </View>
              )}

              {progress?.completed_at && (
                <Text style={styles.completedDate}>
                  Completed:{" "}
                  {new Date(progress.completed_at).toLocaleDateString()}
                </Text>
              )}

              <View style={styles.notesSection}>
                {editingNotes === index ? (
                  <View style={styles.notesInput}>
                    <TextInput
                      style={styles.textInput}
                      placeholder="Add notes about this milestone..."
                      value={noteText}
                      onChangeText={setNoteText}
                      multiline
                      numberOfLines={3}
                    />
                    <View style={styles.notesActions}>
                      <TouchableOpacity
                        style={styles.saveButton}
                        onPress={() => saveNotes(index)}
                      >
                        <Text style={styles.saveButtonText}>Save</Text>
                      </TouchableOpacity>
                      <TouchableOpacity
                        style={styles.cancelButton}
                        onPress={() => {
                          setEditingNotes(null);
                          setNoteText("");
                        }}
                      >
                        <Text style={styles.cancelButtonText}>Cancel</Text>
                      </TouchableOpacity>
                    </View>
                  </View>
                ) : (
                  <TouchableOpacity
                    style={styles.notesButton}
                    onPress={() => {
                      setEditingNotes(index);
                      setNoteText(progress?.notes || "");
                    }}
                  >
                    <Text style={styles.notesButtonText}>
                      {progress?.notes ? "Edit Notes" : "Add Notes"}
                    </Text>
                    {progress?.notes && (
                      <Text style={styles.existingNotes}>{progress.notes}</Text>
                    )}
                  </TouchableOpacity>
                )}
              </View>
            </View>
          );
        })}
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
  loadingText: {
    fontSize: 16,
    color: "#666",
  },
  header: {
    padding: 20,
    backgroundColor: "#fff",
    borderBottomWidth: 1,
    borderBottomColor: "#e0e0e0",
  },
  title: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#333",
    marginBottom: 10,
  },
  description: {
    fontSize: 16,
    color: "#666",
    lineHeight: 24,
    marginBottom: 20,
  },
  progressContainer: {
    marginBottom: 20,
  },
  progressBar: {
    height: 8,
    backgroundColor: "#e0e0e0",
    borderRadius: 4,
    marginBottom: 8,
  },
  progressFill: {
    height: "100%",
    backgroundColor: "#007AFF",
    borderRadius: 4,
  },
  progressText: {
    fontSize: 14,
    color: "#666",
    textAlign: "center",
  },
  dateInfo: {
    flexDirection: "row",
    justifyContent: "space-between",
    flexWrap: "wrap",
  },
  dateText: {
    fontSize: 12,
    color: "#666",
  },
  milestonesContainer: {
    padding: 20,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: "bold",
    color: "#333",
    marginBottom: 15,
  },
  milestoneCard: {
    backgroundColor: "#fff",
    borderRadius: 12,
    padding: 15,
    marginBottom: 15,
    borderWidth: 1,
    borderColor: "#e0e0e0",
    boxShadow: "0 2px 4px rgba(0, 0, 0, 0.1)",
    elevation: 3,
  },
  completedMilestone: {
    backgroundColor: "#f8f9fa",
    borderColor: "#34C759",
  },
  currentMilestone: {
    borderColor: "#007AFF",
    borderWidth: 2,
  },
  milestoneHeader: {
    flexDirection: "row",
    alignItems: "flex-start",
    marginBottom: 10,
  },
  checkbox: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: "#ccc",
    marginRight: 12,
    justifyContent: "center",
    alignItems: "center",
  },
  checkedBox: {
    backgroundColor: "#34C759",
    borderColor: "#34C759",
  },
  checkmark: {
    color: "#fff",
    fontSize: 14,
    fontWeight: "bold",
  },
  milestoneInfo: {
    flex: 1,
  },
  milestoneTitle: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#333",
    marginBottom: 5,
  },
  completedText: {
    textDecorationLine: "line-through",
    color: "#666",
  },
  milestoneDescription: {
    fontSize: 14,
    color: "#666",
    lineHeight: 20,
    marginBottom: 5,
  },
  estimatedHours: {
    fontSize: 12,
    color: "#007AFF",
    fontWeight: "600",
  },
  topicsContainer: {
    marginTop: 10,
    paddingTop: 10,
    borderTopWidth: 1,
    borderTopColor: "#f0f0f0",
  },
  topicsTitle: {
    fontSize: 14,
    fontWeight: "600",
    color: "#333",
    marginBottom: 5,
  },
  topic: {
    fontSize: 14,
    color: "#666",
    marginBottom: 2,
  },
  completedDate: {
    fontSize: 12,
    color: "#34C759",
    fontWeight: "600",
    marginTop: 10,
  },
  notesSection: {
    marginTop: 10,
    paddingTop: 10,
    borderTopWidth: 1,
    borderTopColor: "#f0f0f0",
  },
  notesInput: {
    marginBottom: 10,
  },
  textInput: {
    borderWidth: 1,
    borderColor: "#e0e0e0",
    borderRadius: 8,
    padding: 12,
    fontSize: 14,
    color: "#333",
    textAlignVertical: "top",
    marginBottom: 10,
  },
  notesActions: {
    flexDirection: "row",
    justifyContent: "flex-end",
  },
  saveButton: {
    backgroundColor: "#007AFF",
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 6,
    marginLeft: 10,
  },
  saveButtonText: {
    color: "#fff",
    fontSize: 14,
    fontWeight: "600",
  },
  cancelButton: {
    backgroundColor: "#f0f0f0",
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 6,
    marginLeft: 10,
  },
  cancelButtonText: {
    color: "#666",
    fontSize: 14,
    fontWeight: "600",
  },
  notesButton: {
    padding: 10,
    backgroundColor: "#f8f9fa",
    borderRadius: 6,
  },
  notesButtonText: {
    fontSize: 14,
    color: "#007AFF",
    fontWeight: "600",
  },
  existingNotes: {
    fontSize: 14,
    color: "#333",
    marginTop: 5,
    fontStyle: "italic",
  },
});
