# PLC Learning App 📚

A personalized learning companion app built with Expo, featuring spaced repetition, AI-powered content generation, and smart notifications.

## Features

- 🧠 **Spaced Repetition Learning** - Optimized flashcard review system
- 🤖 **AI-Powered Content** - Generate courses and flashcards with Gemini AI
- 📱 **Smart Notifications** - Study reminders and progress tracking
- 🔐 **Secure Authentication** - Clerk-based user management
- 💾 **Offline Support** - Study even without internet connection
- 📊 **Progress Tracking** - Detailed analytics and achievements

## Get started

1. Install dependencies

   ```bash
   npm install
   ```

2. Start the app

   ```bash
   npx expo start
   ```

## Important: Notifications Setup

⚠️ **If you see notification warnings**, this is because `expo-notifications` push notifications were removed from Expo Go in SDK 53.

### Quick Solutions:

**Option 1: Development Build (Recommended)**

```bash
npm run build:dev
```

This creates a development build with full notifications support.

**Option 2: Run Locally**

```bash
npm run dev:android  # Requires Android Studio
```

**Option 3: Continue with Expo Go**

- Local notifications will work
- Push notifications won't work
- You'll see warnings but the app functions

### Running Options:

- **Development Build** - Full functionality including push notifications
- **Local Development** - `npm run dev:android` or `npm run dev:ios`
- **Expo Go** - Limited notifications, good for quick testing

You can start developing by editing the files inside the **app** directory. This project uses [file-based routing](https://docs.expo.dev/router/introduction).

## Get a fresh project

When you're ready, run:

```bash
npm run reset-project
```

This command will move the starter code to the **app-example** directory and create a blank **app** directory where you can start developing.

## Learn more

To learn more about developing your project with Expo, look at the following resources:

- [Expo documentation](https://docs.expo.dev/): Learn fundamentals, or go into advanced topics with our [guides](https://docs.expo.dev/guides).
- [Learn Expo tutorial](https://docs.expo.dev/tutorial/introduction/): Follow a step-by-step tutorial where you'll create a project that runs on Android, iOS, and the web.

## Join the community

Join our community of developers creating universal apps.

- [Expo on GitHub](https://github.com/expo/expo): View our open source platform and contribute.
- [Discord community](https://chat.expo.dev): Chat with Expo users and ask questions.
