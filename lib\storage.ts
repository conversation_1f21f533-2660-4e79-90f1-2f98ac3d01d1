import { Platform } from "react-native";

// Lazy import AsyncStorage only when needed for native platforms
let AsyncStorage: any = null;

const getAsyncStorage = async () => {
  if (!AsyncStorage && Platform.OS !== "web") {
    AsyncStorage = (await import("@react-native-async-storage/async-storage"))
      .default;
  }
  return AsyncStorage;
};

/**
 * Platform-specific storage adapter that works across web and native platforms
 * Uses localStorage for web and AsyncStorage for native platforms
 */
class PlatformStorage {
  private isWeb = Platform.OS === "web";

  async getItem(key: string): Promise<string | null> {
    try {
      if (this.isWeb) {
        if (typeof window !== "undefined" && window.localStorage) {
          return window.localStorage.getItem(key);
        }
        return null;
      } else {
        const storage = await getAsyncStorage();
        return await storage.getItem(key);
      }
    } catch (error) {
      console.warn(`Storage getItem error for key "${key}":`, error);
      return null;
    }
  }

  async setItem(key: string, value: string): Promise<void> {
    try {
      if (this.isWeb) {
        if (typeof window !== "undefined" && window.localStorage) {
          window.localStorage.setItem(key, value);
        }
      } else {
        const storage = await getAsyncStorage();
        await storage.setItem(key, value);
      }
    } catch (error) {
      console.warn(`Storage setItem error for key "${key}":`, error);
    }
  }

  async removeItem(key: string): Promise<void> {
    try {
      if (this.isWeb) {
        if (typeof window !== "undefined" && window.localStorage) {
          window.localStorage.removeItem(key);
        }
      } else {
        const storage = await getAsyncStorage();
        await storage.removeItem(key);
      }
    } catch (error) {
      console.warn(`Storage removeItem error for key "${key}":`, error);
    }
  }

  async clear(): Promise<void> {
    try {
      if (this.isWeb) {
        if (typeof window !== "undefined" && window.localStorage) {
          window.localStorage.clear();
        }
      } else {
        const storage = await getAsyncStorage();
        await storage.clear();
      }
    } catch (error) {
      console.warn("Storage clear error:", error);
    }
  }

  async getAllKeys(): Promise<string[]> {
    try {
      if (this.isWeb) {
        if (typeof window !== "undefined" && window.localStorage) {
          return Object.keys(window.localStorage);
        }
        return [];
      } else {
        const storage = await getAsyncStorage();
        return await storage.getAllKeys();
      }
    } catch (error) {
      console.warn("Storage getAllKeys error:", error);
      return [];
    }
  }

  async multiGet(keys: string[]): Promise<[string, string | null][]> {
    try {
      if (this.isWeb) {
        if (typeof window !== "undefined" && window.localStorage) {
          return keys.map((key) => [key, window.localStorage.getItem(key)]);
        }
        return keys.map((key) => [key, null]);
      } else {
        const storage = await getAsyncStorage();
        return await storage.multiGet(keys);
      }
    } catch (error) {
      console.warn("Storage multiGet error:", error);
      return keys.map((key) => [key, null]);
    }
  }

  async multiSet(keyValuePairs: [string, string][]): Promise<void> {
    try {
      if (this.isWeb) {
        if (typeof window !== "undefined" && window.localStorage) {
          keyValuePairs.forEach(([key, value]) => {
            window.localStorage.setItem(key, value);
          });
        }
      } else {
        const storage = await getAsyncStorage();
        await storage.multiSet(keyValuePairs);
      }
    } catch (error) {
      console.warn("Storage multiSet error:", error);
    }
  }

  async multiRemove(keys: string[]): Promise<void> {
    try {
      if (this.isWeb) {
        if (typeof window !== "undefined" && window.localStorage) {
          keys.forEach((key) => {
            window.localStorage.removeItem(key);
          });
        }
      } else {
        const storage = await getAsyncStorage();
        await storage.multiRemove(keys);
      }
    } catch (error) {
      console.warn("Storage multiRemove error:", error);
    }
  }
}

// Export a singleton instance
export const platformStorage = new PlatformStorage();

// Export the class for testing or custom instances
export { PlatformStorage };

// Export a function that returns the storage adapter for Supabase
export const createStorageAdapter = () => {
  return {
    getItem: (key: string) => platformStorage.getItem(key),
    setItem: (key: string, value: string) =>
      platformStorage.setItem(key, value),
    removeItem: (key: string) => platformStorage.removeItem(key),
  };
};
