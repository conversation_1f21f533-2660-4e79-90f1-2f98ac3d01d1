# Setup Guide for PLC Learning App

## Notifications Setup

### Current Issue
You're seeing this error because `expo-notifications` remote push notifications were removed from Expo Go starting with SDK 53.

### Solutions

#### Option 1: Development Build (Recommended)
This gives you full notifications functionality:

1. **Build a development build:**
   ```bash
   npm run build:dev
   ```
   This will guide you through creating a development build with EAS.

2. **Or run locally (requires Android Studio):**
   ```bash
   npm run dev:android
   ```

#### Option 2: Continue with Expo Go (Limited)
If you want to continue using Expo Go for now:
- Local notifications will still work
- Push notifications won't work
- You'll see warnings but the app will function

### What's Configured
- ✅ `expo-dev-client` installed
- ✅ Notification plugin configured in app.json
- ✅ Notification service updated to handle Expo Go limitations
- ✅ Build scripts added to package.json

## Clerk Authentication Setup

### Development vs Production Keys

#### Current Status
You're using Clerk development keys, which is correct for development.

#### For Production
When you're ready to deploy:

1. **Create production instance in Clerk Dashboard:**
   - Go to https://dashboard.clerk.com
   - Create a new application or switch to production mode
   - Get your production publishable key

2. **Update environment variables:**
   ```env
   EXPO_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_live_your_production_key_here
   ```

3. **Configure production settings:**
   - Set up your production domain
   - Configure OAuth providers for production
   - Set up webhooks if needed

### Development Setup (Current)
Your current setup is correct for development:
- Development keys are being used
- User authentication is working
- Supabase integration is configured

## Next Steps

### For Full Notifications (Recommended)
1. Run `npm run build:dev` to create a development build
2. Install the generated APK on your Android device
3. Test notifications functionality

### For Production Deployment
1. Set up production Clerk instance
2. Update environment variables
3. Build production version with `eas build --platform android --profile production`

## Troubleshooting

### If Build Fails
- Make sure you have an Expo account
- Install EAS CLI: `npm install -g @expo/eas-cli`
- Login: `eas login`

### If Notifications Don't Work
- Check device permissions
- Verify you're using development build (not Expo Go)
- Check console for error messages

### If Clerk Issues
- Verify your publishable key is correct
- Check network connectivity
- Ensure you're using the right environment (dev/prod)
