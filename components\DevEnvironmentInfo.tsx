import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Alert } from 'react-native';
import Constants from 'expo-constants';
import * as Notifications from 'expo-notifications';
import { ThemedView } from './ThemedView';
import { ThemedText } from './ThemedText';

interface EnvironmentInfoProps {
  onDismiss?: () => void;
}

export function DevEnvironmentInfo({ onDismiss }: EnvironmentInfoProps) {
  const [notificationStatus, setNotificationStatus] = useState<string>('checking');
  const [isExpoGo, setIsExpoGo] = useState(false);

  useEffect(() => {
    checkEnvironment();
  }, []);

  const checkEnvironment = async () => {
    // Check if running in Expo Go
    const expoGo = Constants.appOwnership === 'expo';
    setIsExpoGo(expoGo);

    // Check notification permissions
    try {
      const { status } = await Notifications.getPermissionsAsync();
      setNotificationStatus(status);
    } catch (error) {
      setNotificationStatus('error');
    }
  };

  const requestNotificationPermissions = async () => {
    try {
      const { status } = await Notifications.requestPermissionsAsync();
      setNotificationStatus(status);
      
      if (status === 'granted') {
        Alert.alert(
          'Permissions Granted!',
          'Notifications are now enabled. Note that push notifications require a development build.'
        );
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to request notification permissions');
    }
  };

  const showBuildInstructions = () => {
    Alert.alert(
      'Development Build Required',
      'To use full notification features:\n\n1. Run: npm run build:dev\n2. Install the generated APK\n3. Use that instead of Expo Go\n\nOr run locally with: npm run dev:android',
      [{ text: 'OK' }]
    );
  };

  if (!isExpoGo && notificationStatus === 'granted') {
    // Everything is working fine, don't show the info
    return null;
  }

  return (
    <ThemedView style={styles.container}>
      <ThemedText style={styles.title}>Development Environment</ThemedText>
      
      <View style={styles.statusContainer}>
        <View style={styles.statusItem}>
          <Text style={styles.statusLabel}>Environment:</Text>
          <Text style={[styles.statusValue, isExpoGo ? styles.warning : styles.success]}>
            {isExpoGo ? 'Expo Go' : 'Development Build'}
          </Text>
        </View>
        
        <View style={styles.statusItem}>
          <Text style={styles.statusLabel}>Notifications:</Text>
          <Text style={[
            styles.statusValue,
            notificationStatus === 'granted' ? styles.success : styles.warning
          ]}>
            {notificationStatus === 'granted' ? 'Enabled' : 'Limited'}
          </Text>
        </View>
      </View>

      {isExpoGo && (
        <View style={styles.warningContainer}>
          <ThemedText style={styles.warningText}>
            ⚠️ You're using Expo Go. Push notifications are limited.
          </ThemedText>
          <TouchableOpacity style={styles.button} onPress={showBuildInstructions}>
            <Text style={styles.buttonText}>How to Fix</Text>
          </TouchableOpacity>
        </View>
      )}

      {notificationStatus !== 'granted' && (
        <View style={styles.warningContainer}>
          <ThemedText style={styles.warningText}>
            📱 Notification permissions needed for reminders
          </ThemedText>
          <TouchableOpacity style={styles.button} onPress={requestNotificationPermissions}>
            <Text style={styles.buttonText}>Enable Notifications</Text>
          </TouchableOpacity>
        </View>
      )}

      {onDismiss && (
        <TouchableOpacity style={styles.dismissButton} onPress={onDismiss}>
          <Text style={styles.dismissText}>Dismiss</Text>
        </TouchableOpacity>
      )}
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    margin: 16,
    padding: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  statusContainer: {
    marginBottom: 12,
  },
  statusItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  statusLabel: {
    fontSize: 14,
    color: '#666',
  },
  statusValue: {
    fontSize: 14,
    fontWeight: '500',
  },
  success: {
    color: '#4CAF50',
  },
  warning: {
    color: '#FF9800',
  },
  warningContainer: {
    backgroundColor: '#FFF3E0',
    padding: 12,
    borderRadius: 6,
    marginBottom: 8,
  },
  warningText: {
    fontSize: 14,
    marginBottom: 8,
    color: '#E65100',
  },
  button: {
    backgroundColor: '#2196F3',
    padding: 8,
    borderRadius: 4,
    alignItems: 'center',
  },
  buttonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '500',
  },
  dismissButton: {
    alignItems: 'center',
    padding: 8,
  },
  dismissText: {
    color: '#666',
    fontSize: 14,
  },
});
