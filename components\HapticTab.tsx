import { BottomTabBarButtonProps } from "@react-navigation/bottom-tabs";
import { PlatformPressable } from "@react-navigation/elements";
import * as Haptics from "expo-haptics";
import { Platform } from "react-native";
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withSpring,
  withTiming,
} from "react-native-reanimated";

export function HapticTab(props: BottomTabBarButtonProps) {
  const scale = useSharedValue(1);
  const opacity = useSharedValue(1);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
    opacity: opacity.value,
  }));

  return (
    <Animated.View style={[{ flex: 1 }, animatedStyle]}>
      <PlatformPressable
        {...props}
        onPressIn={(ev) => {
          // Add haptic feedback on iOS
          if (Platform.OS === "ios") {
            Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
          }

          // Add visual feedback
          scale.value = withSpring(0.95, { damping: 15, stiffness: 300 });
          opacity.value = withTiming(0.7, { duration: 100 });

          props.onPressIn?.(ev);
        }}
        onPressOut={(ev) => {
          // Reset visual feedback
          scale.value = withSpring(1, { damping: 15, stiffness: 300 });
          opacity.value = withTiming(1, { duration: 100 });

          props.onPressOut?.(ev);
        }}
        style={[
          props.style,
          {
            flex: 1,
            justifyContent: "center",
            alignItems: "center",
          },
        ]}
      />
    </Animated.View>
  );
}
