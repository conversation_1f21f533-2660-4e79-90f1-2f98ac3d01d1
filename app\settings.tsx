import { notificationService, ReminderSettings } from "@/lib/notifications";
import { router } from "expo-router";
import React, { useEffect, useState } from "react";
import {
  Alert,
  ScrollView,
  StyleSheet,
  Switch,
  Text,
  TouchableOpacity,
  View,
} from "react-native";

export default function SettingsScreen() {
  const [settings, setSettings] = useState<ReminderSettings>({
    dailyStudyReminder: true,
    dailyStudyTime: "19:00",
    flashcardReminder: true,
    flashcardInterval: 24,
    weeklyGoalReminder: true,
    weeklyGoalDay: 0,
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadSettings();
    initializeNotifications();
  }, []);

  const initializeNotifications = async () => {
    const success = await notificationService.initialize();
    if (!success) {
      Alert.alert(
        "Notifications Disabled",
        "Please enable notifications in your device settings to receive study reminders."
      );
    }
  };

  const loadSettings = async () => {
    try {
      const savedSettings = await notificationService.getReminderSettings();
      setSettings(savedSettings);
    } catch (error) {
      console.error("Error loading settings:", error);
    } finally {
      setLoading(false);
    }
  };

  const updateSetting = async (key: keyof ReminderSettings, value: any) => {
    const newSettings = { ...settings, [key]: value };
    setSettings(newSettings);

    try {
      await notificationService.saveReminderSettings(newSettings);
    } catch (error) {
      console.error("Error saving settings:", error);
      Alert.alert("Error", "Failed to save settings");
    }
  };

  const timeOptions = [
    { label: "7:00 AM", value: "07:00" },
    { label: "8:00 AM", value: "08:00" },
    { label: "9:00 AM", value: "09:00" },
    { label: "12:00 PM", value: "12:00" },
    { label: "6:00 PM", value: "18:00" },
    { label: "7:00 PM", value: "19:00" },
    { label: "8:00 PM", value: "20:00" },
    { label: "9:00 PM", value: "21:00" },
  ];

  const intervalOptions = [
    { label: "Every 6 hours", value: 6 },
    { label: "Every 12 hours", value: 12 },
    { label: "Every 24 hours", value: 24 },
    { label: "Every 2 days", value: 48 },
  ];

  const dayOptions = [
    { label: "Sunday", value: 0 },
    { label: "Monday", value: 1 },
    { label: "Tuesday", value: 2 },
    { label: "Wednesday", value: 3 },
    { label: "Thursday", value: 4 },
    { label: "Friday", value: 5 },
    { label: "Saturday", value: 6 },
  ];

  const showTimeSelector = () => {
    Alert.alert(
      "Daily Study Time",
      "Choose your preferred study reminder time:",
      timeOptions.map((option) => ({
        text: option.label,
        onPress: () => updateSetting("dailyStudyTime", option.value),
      }))
    );
  };

  const showIntervalSelector = () => {
    Alert.alert(
      "Flashcard Reminder Interval",
      "How often should we remind you to review flashcards?",
      intervalOptions.map((option) => ({
        text: option.label,
        onPress: () => updateSetting("flashcardInterval", option.value),
      }))
    );
  };

  const showDaySelector = () => {
    Alert.alert(
      "Weekly Goal Reminder",
      "Which day should we remind you about your weekly goals?",
      dayOptions.map((option) => ({
        text: option.label,
        onPress: () => updateSetting("weeklyGoalDay", option.value),
      }))
    );
  };

  const getTimeLabel = (time: string) => {
    const option = timeOptions.find((opt) => opt.value === time);
    return option ? option.label : time;
  };

  const getIntervalLabel = (interval: number) => {
    const option = intervalOptions.find((opt) => opt.value === interval);
    return option ? option.label : `Every ${interval} hours`;
  };

  const getDayLabel = (day: number) => {
    const option = dayOptions.find((opt) => opt.value === day);
    return option ? option.label : "Sunday";
  };

  if (loading) {
    return (
      <View style={styles.centerContainer}>
        <Text style={styles.loadingText}>Loading settings...</Text>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <Text style={styles.backButtonText}>← Back</Text>
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Settings</Text>
      </View>

      {/* Development Environment Info */}
      <View style={styles.devInfoContainer}>
        {/* We'll add the component content directly here for now */}
        <View style={styles.devInfoCard}>
          <Text style={styles.devInfoTitle}>Development Environment</Text>
          <Text style={styles.devInfoText}>
            ⚠️ If you're seeing notification warnings, check the SETUP_GUIDE.md
            file for solutions.
          </Text>
          <Text style={styles.devInfoText}>
            💡 For full notifications: Run 'npm run build:dev' to create a
            development build.
          </Text>
        </View>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Study Reminders</Text>

        <View style={styles.settingItem}>
          <View style={styles.settingInfo}>
            <Text style={styles.settingLabel}>Daily Study Reminder</Text>
            <Text style={styles.settingDescription}>
              Get reminded to study every day
            </Text>
          </View>
          <Switch
            value={settings.dailyStudyReminder}
            onValueChange={(value) =>
              updateSetting("dailyStudyReminder", value)
            }
            trackColor={{ false: "#e0e0e0", true: "#007AFF" }}
            thumbColor="#fff"
          />
        </View>

        {settings.dailyStudyReminder && (
          <TouchableOpacity
            style={styles.settingItem}
            onPress={showTimeSelector}
          >
            <View style={styles.settingInfo}>
              <Text style={styles.settingLabel}>Study Time</Text>
              <Text style={styles.settingDescription}>
                {getTimeLabel(settings.dailyStudyTime)}
              </Text>
            </View>
            <Text style={styles.settingArrow}>›</Text>
          </TouchableOpacity>
        )}

        <View style={styles.settingItem}>
          <View style={styles.settingInfo}>
            <Text style={styles.settingLabel}>Flashcard Reminders</Text>
            <Text style={styles.settingDescription}>
              Get reminded to review flashcards
            </Text>
          </View>
          <Switch
            value={settings.flashcardReminder}
            onValueChange={(value) => updateSetting("flashcardReminder", value)}
            trackColor={{ false: "#e0e0e0", true: "#007AFF" }}
            thumbColor="#fff"
          />
        </View>

        {settings.flashcardReminder && (
          <TouchableOpacity
            style={styles.settingItem}
            onPress={showIntervalSelector}
          >
            <View style={styles.settingInfo}>
              <Text style={styles.settingLabel}>Reminder Frequency</Text>
              <Text style={styles.settingDescription}>
                {getIntervalLabel(settings.flashcardInterval)}
              </Text>
            </View>
            <Text style={styles.settingArrow}>›</Text>
          </TouchableOpacity>
        )}

        <View style={styles.settingItem}>
          <View style={styles.settingInfo}>
            <Text style={styles.settingLabel}>Weekly Goal Reminder</Text>
            <Text style={styles.settingDescription}>
              Weekly check-in on your progress
            </Text>
          </View>
          <Switch
            value={settings.weeklyGoalReminder}
            onValueChange={(value) =>
              updateSetting("weeklyGoalReminder", value)
            }
            trackColor={{ false: "#e0e0e0", true: "#007AFF" }}
            thumbColor="#fff"
          />
        </View>

        {settings.weeklyGoalReminder && (
          <TouchableOpacity
            style={styles.settingItem}
            onPress={showDaySelector}
          >
            <View style={styles.settingInfo}>
              <Text style={styles.settingLabel}>Reminder Day</Text>
              <Text style={styles.settingDescription}>
                {getDayLabel(settings.weeklyGoalDay)}
              </Text>
            </View>
            <Text style={styles.settingArrow}>›</Text>
          </TouchableOpacity>
        )}
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Other Settings</Text>

        <TouchableOpacity
          style={styles.settingItem}
          onPress={() => router.push("/notification-history")}
        >
          <View style={styles.settingInfo}>
            <Text style={styles.settingLabel}>Notification History</Text>
            <Text style={styles.settingDescription}>
              View your notification history
            </Text>
          </View>
          <Text style={styles.settingArrow}>›</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.settingItem}
          onPress={() => {
            Alert.alert(
              "Clear All Notifications",
              "This will cancel all scheduled notifications. Are you sure?",
              [
                { text: "Cancel", style: "cancel" },
                {
                  text: "Clear",
                  style: "destructive",
                  onPress: async () => {
                    await notificationService.cancelAllNotifications();
                    Alert.alert(
                      "Success",
                      "All notifications have been cleared."
                    );
                  },
                },
              ]
            );
          }}
        >
          <View style={styles.settingInfo}>
            <Text style={[styles.settingLabel, { color: "#FF3B30" }]}>
              Clear All Notifications
            </Text>
            <Text style={styles.settingDescription}>
              Cancel all scheduled notifications
            </Text>
          </View>
          <Text style={styles.settingArrow}>›</Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f5f5f5",
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    padding: 20,
    paddingTop: 60,
    backgroundColor: "#fff",
    borderBottomWidth: 1,
    borderBottomColor: "#e0e0e0",
  },
  backButton: {
    marginRight: 15,
  },
  backButtonText: {
    fontSize: 16,
    color: "#007AFF",
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: "bold",
    color: "#333",
  },
  centerContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  loadingText: {
    fontSize: 16,
    color: "#666",
  },
  section: {
    backgroundColor: "#fff",
    marginTop: 20,
    marginHorizontal: 20,
    borderRadius: 12,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#333",
    padding: 20,
    paddingBottom: 10,
  },
  settingItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: "#f0f0f0",
  },
  settingInfo: {
    flex: 1,
    marginRight: 15,
  },
  settingLabel: {
    fontSize: 16,
    color: "#333",
    marginBottom: 2,
  },
  settingDescription: {
    fontSize: 14,
    color: "#666",
  },
  settingArrow: {
    fontSize: 20,
    color: "#ccc",
  },
  devInfoContainer: {
    marginHorizontal: 20,
    marginTop: 10,
  },
  devInfoCard: {
    backgroundColor: "#FFF3E0",
    padding: 16,
    borderRadius: 8,
    borderLeftWidth: 4,
    borderLeftColor: "#FF9800",
  },
  devInfoTitle: {
    fontSize: 16,
    fontWeight: "bold",
    color: "#E65100",
    marginBottom: 8,
  },
  devInfoText: {
    fontSize: 14,
    color: "#E65100",
    marginBottom: 4,
    lineHeight: 20,
  },
});
