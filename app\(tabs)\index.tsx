import { useThemeColor } from "@/hooks/useThemeColor";
import { supabase } from "@/lib/supabase";
import { useUser } from "@clerk/clerk-expo";
import { router } from "expo-router";
import React, { useEffect, useState } from "react";
import {
  Dimensions,
  Platform,
  RefreshControl,
  ScrollView,
  StatusBar,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";

const { width: screenWidth } = Dimensions.get("window");

interface DashboardStats {
  totalCourses: number;
  totalFlashcards: number;
  studyStreak: number;
  weeklyProgress: number;
}

// Helper function to get time-based greeting
const getTimeBasedGreeting = () => {
  const hour = new Date().getHours();
  if (hour < 12) return "Good morning";
  if (hour < 17) return "Good afternoon";
  return "Good evening";
};

// Motivational quotes array
const motivationalQuotes = [
  "Every expert was once a beginner.",
  "Learning never exhausts the mind.",
  "The beautiful thing about learning is that no one can take it away from you.",
  "Education is the most powerful weapon which you can use to change the world.",
  "Live as if you were to die tomorrow. Learn as if you were to live forever.",
];

export default function DashboardScreen() {
  const { user } = useUser();

  // Theme colors
  const backgroundColor = useThemeColor({}, "background");
  const textColor = useThemeColor({}, "text");
  const cardBackground = useThemeColor({}, "cardBackground");
  const surfaceBackground = useThemeColor({}, "surfaceBackground");
  const [stats, setStats] = useState<DashboardStats>({
    totalCourses: 0,
    totalFlashcards: 0,
    studyStreak: 0,
    weeklyProgress: 0,
  });
  const [recentCourses, setRecentCourses] = useState([]);
  const [upcomingReviews, setUpcomingReviews] = useState([]);
  const [refreshing, setRefreshing] = useState(false);
  const [currentQuote, setCurrentQuote] = useState("");
  const [greeting, setGreeting] = useState("");

  // Initialize greeting and quote
  useEffect(() => {
    setGreeting(getTimeBasedGreeting());
    setCurrentQuote(
      motivationalQuotes[Math.floor(Math.random() * motivationalQuotes.length)]
    );
  }, []);

  useEffect(() => {
    if (user) {
      loadDashboardData();
    }
  }, [user]);

  const loadDashboardData = async () => {
    try {
      // Get user from Supabase
      const { data: supabaseUser } = await supabase
        .from("users")
        .select("id")
        .eq("clerk_user_id", user?.id)
        .single();

      if (!supabaseUser) return;

      // Load stats
      const [coursesResult, flashcardsResult, sessionsResult] =
        await Promise.all([
          supabase.from("courses").select("id").eq("user_id", supabaseUser.id),
          supabase
            .from("flashcards")
            .select("id")
            .eq("user_id", supabaseUser.id),
          supabase
            .from("study_sessions")
            .select("completed_at")
            .eq("user_id", supabaseUser.id)
            .gte(
              "completed_at",
              new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString()
            ),
        ]);

      setStats({
        totalCourses: coursesResult.data?.length || 0,
        totalFlashcards: flashcardsResult.data?.length || 0,
        studyStreak: calculateStreak(sessionsResult.data || []),
        weeklyProgress: sessionsResult.data?.length || 0,
      });

      // Load recent courses
      const { data: courses } = await supabase
        .from("courses")
        .select("*")
        .eq("user_id", supabaseUser.id)
        .order("created_at", { ascending: false })
        .limit(3);

      setRecentCourses(courses || []);

      // Load upcoming flashcard reviews
      const { data: reviews } = await supabase
        .from("flashcards")
        .select("*")
        .eq("user_id", supabaseUser.id)
        .lte("next_review_date", new Date().toISOString())
        .limit(5);

      setUpcomingReviews(reviews || []);
    } catch (error) {
      console.error("Error loading dashboard data:", error);
    }
  };

  const calculateStreak = (sessions: any[]) => {
    // Simple streak calculation - count consecutive days with sessions
    const today = new Date();
    let streak = 0;

    for (let i = 0; i < 30; i++) {
      const checkDate = new Date(today);
      checkDate.setDate(today.getDate() - i);
      const dateStr = checkDate.toDateString();

      const hasSession = sessions.some(
        (session) => new Date(session.completed_at).toDateString() === dateStr
      );

      if (hasSession) {
        streak++;
      } else if (i > 0) {
        break;
      }
    }

    return streak;
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadDashboardData();
    setRefreshing(false);
  };

  return (
    <ScrollView
      style={[styles.container, { backgroundColor: surfaceBackground }]}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
      }
      showsVerticalScrollIndicator={false}
    >
      <View style={styles.header}>
        <View style={styles.headerContent}>
          <View style={styles.greetingContainer}>
            <Text style={styles.timeGreeting}>
              {greeting}, {user?.firstName || "Learner"}! 👋
            </Text>
            <Text style={styles.subtitle}>
              Ready to continue your learning journey?
            </Text>
          </View>

          {currentQuote && (
            <View style={styles.quoteContainer}>
              <Text style={styles.quoteIcon}>💡</Text>
              <Text style={styles.quote}>"{currentQuote}"</Text>
            </View>
          )}
        </View>
      </View>

      <View
        style={[styles.statsContainer, { backgroundColor: cardBackground }]}
      >
        <View style={styles.statCard}>
          <View style={styles.statIconContainer}>
            <Text style={styles.statIcon}>📚</Text>
          </View>
          <Text style={[styles.statNumber, { color: textColor }]}>
            {stats.totalCourses}
          </Text>
          <Text style={styles.statLabel}>Courses</Text>
        </View>
        <View style={styles.statCard}>
          <View style={styles.statIconContainer}>
            <Text style={styles.statIcon}>🃏</Text>
          </View>
          <Text style={[styles.statNumber, { color: textColor }]}>
            {stats.totalFlashcards}
          </Text>
          <Text style={styles.statLabel}>Flashcards</Text>
        </View>
        <View style={styles.statCard}>
          <View style={styles.statIconContainer}>
            <Text style={styles.statIcon}>🔥</Text>
          </View>
          <Text style={[styles.statNumber, { color: textColor }]}>
            {stats.studyStreak}
          </Text>
          <Text style={styles.statLabel}>Day Streak</Text>
        </View>
        <View style={styles.statCard}>
          <View style={styles.statIconContainer}>
            <Text style={styles.statIcon}>📈</Text>
          </View>
          <Text style={[styles.statNumber, { color: textColor }]}>
            {stats.weeklyProgress}
          </Text>
          <Text style={styles.statLabel}>This Week</Text>
        </View>
      </View>

      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: textColor }]}>
          Quick Actions
        </Text>
        <View style={styles.actionButtons}>
          <TouchableOpacity
            style={[styles.actionButton, styles.primaryActionButton]}
            onPress={() => router.push("/upload-course")}
            activeOpacity={0.8}
          >
            <View style={styles.actionButtonContent}>
              <Text style={styles.actionButtonIcon}>📤</Text>
              <Text style={styles.actionButtonText}>Upload Course</Text>
              <Text style={styles.actionButtonSubtext}>Add new material</Text>
            </View>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.actionButton, styles.secondaryActionButton]}
            onPress={() => router.push("/(tabs)/flashcards")}
            activeOpacity={0.8}
          >
            <View style={styles.actionButtonContent}>
              <Text style={styles.actionButtonIcon}>🧠</Text>
              <Text style={styles.actionButtonText}>Review Cards</Text>
              <Text style={styles.actionButtonSubtext}>Study flashcards</Text>
            </View>
          </TouchableOpacity>
        </View>

        {/* Additional quick actions */}
        <View style={styles.quickActionsGrid}>
          <TouchableOpacity
            style={[
              styles.quickActionItem,
              { backgroundColor: cardBackground },
            ]}
            onPress={() => router.push("/(tabs)/progress")}
            activeOpacity={0.7}
          >
            <Text style={styles.quickActionIcon}>📊</Text>
            <Text style={[styles.quickActionText, { color: textColor }]}>
              Progress
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[
              styles.quickActionItem,
              { backgroundColor: cardBackground },
            ]}
            onPress={() => router.push("/achievements")}
            activeOpacity={0.7}
          >
            <Text style={styles.quickActionIcon}>🏆</Text>
            <Text style={[styles.quickActionText, { color: textColor }]}>
              Achievements
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[
              styles.quickActionItem,
              { backgroundColor: cardBackground },
            ]}
            onPress={() => router.push("/(tabs)/profile")}
            activeOpacity={0.7}
          >
            <Text style={styles.quickActionIcon}>⚙️</Text>
            <Text style={[styles.quickActionText, { color: textColor }]}>
              Settings
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[
              styles.quickActionItem,
              { backgroundColor: cardBackground },
            ]}
            onPress={() => router.push("/help")}
            activeOpacity={0.7}
          >
            <Text style={styles.quickActionIcon}>❓</Text>
            <Text style={[styles.quickActionText, { color: textColor }]}>
              Help
            </Text>
          </TouchableOpacity>
        </View>
      </View>

      {recentCourses.length > 0 && (
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: textColor }]}>
            Recent Courses
          </Text>
          {recentCourses.map((course: any) => (
            <TouchableOpacity
              key={course.id}
              style={[styles.courseCard, { backgroundColor: cardBackground }]}
              onPress={() => router.push(`/course/${course.id}`)}
              activeOpacity={0.8}
            >
              <View style={styles.courseCardHeader}>
                <Text style={styles.courseIcon}>📖</Text>
                <View style={styles.courseInfo}>
                  <Text style={[styles.courseTitle, { color: textColor }]}>
                    {course.title}
                  </Text>
                  <Text style={styles.courseDescription}>
                    {course.description}
                  </Text>
                </View>
                <Text style={styles.courseArrow}>→</Text>
              </View>
            </TouchableOpacity>
          ))}
        </View>
      )}

      {upcomingReviews.length > 0 && (
        <View style={styles.section}>
          <View
            style={[styles.reviewCard, { backgroundColor: cardBackground }]}
          >
            <View style={styles.reviewHeader}>
              <Text style={styles.reviewIcon}>⏰</Text>
              <View style={styles.reviewInfo}>
                <Text
                  style={[
                    styles.sectionTitle,
                    { color: textColor, marginBottom: 5 },
                  ]}
                >
                  Due for Review
                </Text>
                <Text style={styles.reviewCount}>
                  {upcomingReviews.length} flashcards ready for review
                </Text>
              </View>
            </View>
            <TouchableOpacity
              style={styles.reviewButton}
              onPress={() => router.push("/flashcard-review")}
              activeOpacity={0.8}
            >
              <Text style={styles.reviewButtonText}>Start Review Session</Text>
              <Text style={styles.reviewButtonIcon}>🚀</Text>
            </TouchableOpacity>
          </View>
        </View>
      )}

      {/* Empty state when no courses */}
      {recentCourses.length === 0 && (
        <View style={styles.section}>
          <View
            style={[styles.emptyStateCard, { backgroundColor: cardBackground }]}
          >
            <Text style={styles.emptyStateIcon}>🎯</Text>
            <Text style={[styles.emptyStateTitle, { color: textColor }]}>
              Start Your Learning Journey
            </Text>
            <Text style={styles.emptyStateDescription}>
              Upload your first course material to begin creating personalized
              study plans and flashcards.
            </Text>
            <TouchableOpacity
              style={styles.emptyStateButton}
              onPress={() => router.push("/upload-course")}
              activeOpacity={0.8}
            >
              <Text style={styles.emptyStateButtonText}>
                Upload Your First Course
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      )}
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingTop: Platform.OS === "ios" ? 60 : StatusBar.currentHeight + 20,
    paddingBottom: 30,
    paddingHorizontal: 20,
    backgroundColor: "#007AFF",
    borderBottomLeftRadius: 24,
    borderBottomRightRadius: 24,
  },
  headerContent: {
    flex: 1,
  },
  greetingContainer: {
    marginBottom: 20,
  },
  timeGreeting: {
    fontSize: 26,
    fontWeight: "bold",
    color: "#fff",
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: "#fff",
    opacity: 0.9,
    lineHeight: 22,
  },
  quoteContainer: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "rgba(255, 255, 255, 0.15)",
    padding: 16,
    borderRadius: 12,
    marginTop: 10,
  },
  quoteIcon: {
    fontSize: 20,
    marginRight: 12,
  },
  quote: {
    flex: 1,
    fontSize: 14,
    color: "#fff",
    fontStyle: "italic",
    lineHeight: 20,
  },
  statsContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    padding: 20,
    marginTop: -20,
    marginHorizontal: 20,
    borderRadius: 16,
  },
  statCard: {
    alignItems: "center",
    flex: 1,
  },
  statIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "rgba(0, 122, 255, 0.1)",
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 8,
  },
  statIcon: {
    fontSize: 20,
  },
  statNumber: {
    fontSize: 24,
    fontWeight: "bold",
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: "#8E8E93",
    textAlign: "center",
    fontWeight: "500",
  },
  section: {
    margin: 20,
    marginTop: 30,
  },
  sectionTitle: {
    fontSize: 22,
    fontWeight: "bold",
    marginBottom: 16,
  },
  actionButtons: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 20,
  },
  actionButton: {
    flex: 0.48,
    borderRadius: 16,
    overflow: "hidden",
  },
  primaryActionButton: {
    backgroundColor: "#007AFF",
  },
  secondaryActionButton: {
    backgroundColor: "#5856D6",
  },
  actionButtonContent: {
    padding: 20,
    alignItems: "center",
  },
  actionButtonIcon: {
    fontSize: 24,
    marginBottom: 8,
  },
  actionButtonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 4,
  },
  actionButtonSubtext: {
    color: "#fff",
    fontSize: 12,
    opacity: 0.8,
  },
  quickActionsGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "space-between",
  },
  quickActionItem: {
    width: (screenWidth - 60) / 4,
    aspectRatio: 1,
    borderRadius: 12,
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 12,
  },
  quickActionIcon: {
    fontSize: 24,
    marginBottom: 6,
  },
  quickActionText: {
    fontSize: 11,
    fontWeight: "500",
    textAlign: "center",
  },
  courseCard: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
  },
  courseCardHeader: {
    flexDirection: "row",
    alignItems: "center",
  },
  courseIcon: {
    fontSize: 24,
    marginRight: 12,
  },
  courseInfo: {
    flex: 1,
  },
  courseTitle: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 4,
  },
  courseDescription: {
    fontSize: 14,
    color: "#8E8E93",
    lineHeight: 18,
  },
  courseArrow: {
    fontSize: 16,
    color: "#8E8E93",
  },
  reviewCard: {
    padding: 20,
    borderRadius: 16,
  },
  reviewHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 16,
  },
  reviewIcon: {
    fontSize: 28,
    marginRight: 12,
  },
  reviewInfo: {
    flex: 1,
  },
  reviewCount: {
    fontSize: 14,
    color: "#8E8E93",
  },
  reviewButton: {
    backgroundColor: "#34C759",
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderRadius: 12,
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
  },
  reviewButtonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
    marginRight: 8,
  },
  reviewButtonIcon: {
    fontSize: 16,
  },
  emptyStateCard: {
    padding: 32,
    borderRadius: 16,
    alignItems: "center",
  },
  emptyStateIcon: {
    fontSize: 48,
    marginBottom: 16,
  },
  emptyStateTitle: {
    fontSize: 20,
    fontWeight: "bold",
    marginBottom: 12,
    textAlign: "center",
  },
  emptyStateDescription: {
    fontSize: 14,
    color: "#8E8E93",
    textAlign: "center",
    lineHeight: 20,
    marginBottom: 24,
  },
  emptyStateButton: {
    backgroundColor: "#007AFF",
    paddingVertical: 14,
    paddingHorizontal: 24,
    borderRadius: 12,
  },
  emptyStateButtonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
  },
});
