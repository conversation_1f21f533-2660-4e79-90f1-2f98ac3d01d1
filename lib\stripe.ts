// Stripe payment service for subscription management
// Note: This is a simplified implementation for demonstration
// In a real app, you would use @stripe/stripe-react-native

export interface SubscriptionPlan {
  id: string;
  name: string;
  price: number;
  interval: 'month' | 'year';
  features: string[];
  stripePriceId: string;
}

export interface PaymentMethod {
  id: string;
  type: 'card';
  card: {
    brand: string;
    last4: string;
    expMonth: number;
    expYear: number;
  };
}

export interface Subscription {
  id: string;
  status: 'active' | 'canceled' | 'past_due' | 'incomplete';
  currentPeriodEnd: string;
  cancelAtPeriodEnd: boolean;
  plan: SubscriptionPlan;
}

export const SUBSCRIPTION_PLANS: SubscriptionPlan[] = [
  {
    id: 'free',
    name: 'Free',
    price: 0,
    interval: 'month',
    features: [
      'Up to 50 flashcards',
      'Basic study plans',
      'Limited analytics',
      'Email support'
    ],
    stripePriceId: '',
  },
  {
    id: 'premium',
    name: 'Premium',
    price: 9.99,
    interval: 'month',
    features: [
      'Unlimited flashcards',
      'Advanced study plans',
      'Detailed analytics',
      'Priority support',
      'Offline access',
      'Custom themes'
    ],
    stripePriceId: 'price_premium_monthly',
  },
  {
    id: 'premium_yearly',
    name: 'Premium (Yearly)',
    price: 99.99,
    interval: 'year',
    features: [
      'Unlimited flashcards',
      'Advanced study plans',
      'Detailed analytics',
      'Priority support',
      'Offline access',
      'Custom themes',
      '2 months free!'
    ],
    stripePriceId: 'price_premium_yearly',
  },
  {
    id: 'pro',
    name: 'Pro',
    price: 19.99,
    interval: 'month',
    features: [
      'Everything in Premium',
      'AI-powered insights',
      'Advanced gamification',
      'Team collaboration',
      'API access',
      'White-label options'
    ],
    stripePriceId: 'price_pro_monthly',
  },
];

class StripeService {
  private static instance: StripeService;

  static getInstance(): StripeService {
    if (!StripeService.instance) {
      StripeService.instance = new StripeService();
    }
    return StripeService.instance;
  }

  // Initialize Stripe (would normally use Stripe SDK)
  async initialize(publishableKey: string): Promise<void> {
    // In a real implementation:
    // await initStripe({ publishableKey });
    console.log('Stripe initialized with key:', publishableKey);
  }

  // Create payment intent for subscription
  async createPaymentIntent(planId: string, customerId?: string): Promise<{
    clientSecret: string;
    paymentIntentId: string;
  }> {
    try {
      // In a real implementation, this would call your backend API
      // which would create a payment intent using Stripe's server SDK
      
      const response = await fetch('/api/create-payment-intent', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          planId,
          customerId,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to create payment intent');
      }

      const data = await response.json();
      return {
        clientSecret: data.clientSecret,
        paymentIntentId: data.paymentIntentId,
      };
    } catch (error) {
      console.error('Error creating payment intent:', error);
      throw error;
    }
  }

  // Process payment (simplified for demo)
  async processPayment(
    clientSecret: string,
    paymentMethodId?: string
  ): Promise<{ success: boolean; error?: string }> {
    try {
      // In a real implementation:
      // const { error } = await confirmPayment(clientSecret, {
      //   paymentMethodType: 'Card',
      //   paymentMethodData: { ... }
      // });

      // Simulate payment processing
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Simulate success (90% success rate for demo)
      const success = Math.random() > 0.1;
      
      if (success) {
        return { success: true };
      } else {
        return { 
          success: false, 
          error: 'Your card was declined. Please try a different payment method.' 
        };
      }
    } catch (error) {
      console.error('Error processing payment:', error);
      return { 
        success: false, 
        error: 'An unexpected error occurred. Please try again.' 
      };
    }
  }

  // Get customer's payment methods
  async getPaymentMethods(customerId: string): Promise<PaymentMethod[]> {
    try {
      // In a real implementation, this would call your backend
      const response = await fetch(`/api/payment-methods/${customerId}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch payment methods');
      }

      const data = await response.json();
      return data.paymentMethods || [];
    } catch (error) {
      console.error('Error fetching payment methods:', error);
      return [];
    }
  }

  // Get customer's current subscription
  async getSubscription(customerId: string): Promise<Subscription | null> {
    try {
      // In a real implementation, this would call your backend
      const response = await fetch(`/api/subscription/${customerId}`);
      
      if (!response.ok) {
        if (response.status === 404) {
          return null; // No subscription found
        }
        throw new Error('Failed to fetch subscription');
      }

      const data = await response.json();
      return data.subscription;
    } catch (error) {
      console.error('Error fetching subscription:', error);
      return null;
    }
  }

  // Cancel subscription
  async cancelSubscription(subscriptionId: string): Promise<{ success: boolean; error?: string }> {
    try {
      const response = await fetch(`/api/subscription/${subscriptionId}/cancel`, {
        method: 'POST',
      });

      if (!response.ok) {
        throw new Error('Failed to cancel subscription');
      }

      return { success: true };
    } catch (error) {
      console.error('Error canceling subscription:', error);
      return { 
        success: false, 
        error: 'Failed to cancel subscription. Please try again.' 
      };
    }
  }

  // Update subscription
  async updateSubscription(
    subscriptionId: string, 
    newPlanId: string
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const response = await fetch(`/api/subscription/${subscriptionId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          planId: newPlanId,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to update subscription');
      }

      return { success: true };
    } catch (error) {
      console.error('Error updating subscription:', error);
      return { 
        success: false, 
        error: 'Failed to update subscription. Please try again.' 
      };
    }
  }

  // Get plan by ID
  getPlan(planId: string): SubscriptionPlan | undefined {
    return SUBSCRIPTION_PLANS.find(plan => plan.id === planId);
  }

  // Get all available plans
  getPlans(): SubscriptionPlan[] {
    return SUBSCRIPTION_PLANS;
  }

  // Format price for display
  formatPrice(price: number, interval: 'month' | 'year'): string {
    if (price === 0) return 'Free';
    
    const formatted = new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(price);

    return `${formatted}/${interval}`;
  }
}

export const stripeService = StripeService.getInstance();
