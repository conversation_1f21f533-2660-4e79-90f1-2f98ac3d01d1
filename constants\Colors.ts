/**
 * Below are the colors that are used in the app. The colors are defined in the light and dark mode.
 * There are many other ways to style your app. For example, [Nativewind](https://www.nativewind.dev/), [Tamagui](https://tamagui.dev/), [unistyles](https://reactnativeunistyles.vercel.app), etc.
 */

const tintColorLight = "#007AFF";
const tintColorDark = "#0A84FF";

export const Colors = {
  light: {
    text: "#11181C",
    background: "#fff",
    tint: tintColorLight,
    icon: "#8E8E93",
    tabIconDefault: "#8E8E93",
    tabIconSelected: tintColorLight,
    tabBarBackground: "#F8F9FA",
    tabBarBorder: "#E5E5EA",
    tabBarShadow: "rgba(0, 0, 0, 0.1)",
    // Enhanced colors for home screen
    primary: "#007AFF",
    secondary: "#5856D6",
    success: "#34C759",
    warning: "#FF9500",
    error: "#FF3B30",
    cardBackground: "#FFFFFF",
    surfaceBackground: "#F2F2F7",
    gradientStart: "#007AFF",
    gradientEnd: "#5856D6",
    textSecondary: "#8E8E93",
    textTertiary: "#C7C7CC",
    border: "#E5E5EA",
    shadow: "rgba(0, 0, 0, 0.1)",
  },
  dark: {
    text: "#ECEDEE",
    background: "#000000",
    tint: tintColorDark,
    icon: "#8E8E93",
    tabIconDefault: "#8E8E93",
    tabIconSelected: tintColorDark,
    tabBarBackground: "#1C1C1E",
    tabBarBorder: "#38383A",
    tabBarShadow: "rgba(0, 0, 0, 0.3)",
    // Enhanced colors for home screen
    primary: "#0A84FF",
    secondary: "#5E5CE6",
    success: "#30D158",
    warning: "#FF9F0A",
    error: "#FF453A",
    cardBackground: "#1C1C1E",
    surfaceBackground: "#000000",
    gradientStart: "#0A84FF",
    gradientEnd: "#5E5CE6",
    textSecondary: "#8E8E93",
    textTertiary: "#48484A",
    border: "#38383A",
    shadow: "rgba(0, 0, 0, 0.3)",
  },
};
