import { supabase } from "@/lib/supabase";
import { useUser } from "@clerk/clerk-expo";
import { router } from "expo-router";
import React, { useEffect, useState } from "react";
import {
  Alert,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";

interface SubscriptionPlan {
  id: string;
  name: string;
  price: string;
  period: string;
  features: string[];
  popular?: boolean;
}

interface UserSubscription {
  subscription_tier: "free" | "premium" | "pro";
  subscription_expires_at: string | null;
}

const subscriptionPlans: SubscriptionPlan[] = [
  {
    id: "free",
    name: "Free",
    price: "$0",
    period: "forever",
    features: [
      "Upload up to 3 courses",
      "Basic flashcard reviews",
      "Simple progress tracking",
      "Limited AI features",
    ],
  },
  {
    id: "premium",
    name: "Premium",
    price: "$9.99",
    period: "month",
    popular: true,
    features: [
      "Unlimited course uploads",
      "Advanced AI study plans",
      "Smart spaced repetition",
      "Detailed analytics",
      "Priority support",
      "Export study materials",
    ],
  },
  {
    id: "pro",
    name: "Pro",
    price: "$99.99",
    period: "year",
    features: [
      "Everything in Premium",
      "Advanced AI tutoring",
      "Custom study schedules",
      "Team collaboration",
      "API access",
      "White-label options",
    ],
  },
];

export default function SubscriptionScreen() {
  const { user } = useUser();
  const [currentSubscription, setCurrentSubscription] =
    useState<UserSubscription | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (user) {
      loadSubscription();
    }
  }, [user]);

  const loadSubscription = async () => {
    try {
      const { data, error } = await supabase
        .from("users")
        .select("subscription_tier, subscription_expires_at")
        .eq("clerk_user_id", user?.id)
        .single();

      if (error) {
        console.error("Error loading subscription:", error);
        return;
      }

      setCurrentSubscription(data);
    } catch (error) {
      console.error("Error loading subscription:", error);
    } finally {
      setLoading(false);
    }
  };

  const isSubscriptionActive = (
    subscription: UserSubscription | null
  ): boolean => {
    if (!subscription || subscription.subscription_tier === "free") {
      return false;
    }

    if (!subscription.subscription_expires_at) {
      return true; // Lifetime subscription
    }

    return new Date(subscription.subscription_expires_at) > new Date();
  };

  const getCurrentPlanId = (): string => {
    if (!currentSubscription) return "free";

    if (!isSubscriptionActive(currentSubscription)) {
      return "free";
    }

    return currentSubscription.subscription_tier;
  };

  const handleSubscribe = async (planId: string) => {
    if (planId === "free") {
      Alert.alert("Free Plan", "You are already on the free plan!");
      return;
    }

    const plan = stripeService.getPlan(planId);
    if (!plan) {
      Alert.alert("Error", "Invalid plan selected");
      return;
    }

    Alert.alert(
      `Subscribe to ${plan.name}`,
      `This would normally process a payment of ${stripeService.formatPrice(
        plan.price,
        plan.interval
      )} through Stripe. For this demo, we'll simulate the subscription.`,
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Simulate Payment",
          onPress: async () => {
            try {
              // Simulate payment processing
              const result = await stripeService.processPayment(
                "demo_client_secret"
              );

              if (result.success) {
                // Update user's subscription in database
                const expiryDate = new Date();
                if (plan.interval === "month") {
                  expiryDate.setMonth(expiryDate.getMonth() + 1);
                } else {
                  expiryDate.setFullYear(expiryDate.getFullYear() + 1);
                }

                const { error } = await supabase
                  .from("users")
                  .update({
                    subscription_tier: planId,
                    subscription_expires_at: expiryDate.toISOString(),
                  })
                  .eq("clerk_user_id", user?.id);

                if (error) {
                  Alert.alert("Error", "Failed to update subscription");
                  return;
                }

                await loadSubscription();
                Alert.alert(
                  "Success!",
                  `You've successfully subscribed to ${plan.name}!`
                );
              } else {
                Alert.alert(
                  "Payment Failed",
                  result.error || "Unknown error occurred"
                );
              }
            } catch (error) {
              console.error("Payment error:", error);
              Alert.alert("Error", "Failed to process payment");
            }
          },
        },
      ]
    );
  };

  const handleManageSubscription = () => {
    Alert.alert(
      "Manage Subscription",
      "Subscription management will be available soon.",
      [
        {
          text: "Cancel Subscription",
          style: "destructive",
          onPress: () => {
            Alert.alert(
              "Cancel Subscription",
              "Are you sure you want to cancel your subscription? You will lose access to premium features.",
              [
                { text: "Keep Subscription", style: "cancel" },
                {
                  text: "Cancel",
                  style: "destructive",
                  onPress: async () => {
                    // Update subscription in database
                    await supabase
                      .from("users")
                      .update({
                        subscription_tier: "free",
                        subscription_expires_at: null,
                      })
                      .eq("clerk_user_id", user?.id);

                    await loadSubscription();
                    Alert.alert(
                      "Subscription Cancelled",
                      "Your subscription has been cancelled."
                    );
                  },
                },
              ]
            );
          },
        },
        { text: "Close" },
      ]
    );
  };

  const renderPlanCard = (plan: SubscriptionPlan) => {
    const isCurrentPlan = getCurrentPlanId() === plan.id;
    const isActive = isSubscriptionActive(currentSubscription);

    return (
      <View
        key={plan.id}
        style={[
          styles.planCard,
          plan.popular && styles.popularPlan,
          isCurrentPlan && styles.currentPlan,
        ]}
      >
        {plan.popular && (
          <View style={styles.popularBadge}>
            <Text style={styles.popularBadgeText}>MOST POPULAR</Text>
          </View>
        )}

        <Text style={styles.planName}>{plan.name}</Text>
        <View style={styles.priceContainer}>
          <Text style={styles.planPrice}>{plan.price}</Text>
          <Text style={styles.planPeriod}>/{plan.period}</Text>
        </View>

        <View style={styles.featuresContainer}>
          {plan.features.map((feature, index) => (
            <View key={index} style={styles.featureItem}>
              <Text style={styles.featureCheck}>✓</Text>
              <Text style={styles.featureText}>{feature}</Text>
            </View>
          ))}
        </View>

        <TouchableOpacity
          style={[
            styles.subscribeButton,
            isCurrentPlan && styles.currentPlanButton,
          ]}
          onPress={() => {
            if (isCurrentPlan && isActive) {
              handleManageSubscription();
            } else {
              handleSubscribe(plan.id);
            }
          }}
        >
          <Text
            style={[
              styles.subscribeButtonText,
              isCurrentPlan && styles.currentPlanButtonText,
            ]}
          >
            {isCurrentPlan && isActive
              ? "Manage"
              : isCurrentPlan
              ? "Current Plan"
              : plan.id === "free"
              ? "Current Plan"
              : "Subscribe"}
          </Text>
        </TouchableOpacity>
      </View>
    );
  };

  if (loading) {
    return (
      <View style={styles.centerContainer}>
        <Text style={styles.loadingText}>Loading subscription...</Text>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <Text style={styles.backButtonText}>← Back</Text>
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Subscription</Text>
      </View>

      <View style={styles.content}>
        <Text style={styles.title}>Choose Your Plan</Text>
        <Text style={styles.subtitle}>
          Unlock the full potential of your learning journey
        </Text>

        {currentSubscription && (
          <View style={styles.currentStatus}>
            <Text style={styles.statusTitle}>Current Status</Text>
            <Text style={styles.statusText}>
              {currentSubscription.subscription_tier.charAt(0).toUpperCase() +
                currentSubscription.subscription_tier.slice(1)}{" "}
              Plan
            </Text>
            {currentSubscription.subscription_expires_at && (
              <Text style={styles.expiryText}>
                {isSubscriptionActive(currentSubscription)
                  ? `Expires: ${new Date(
                      currentSubscription.subscription_expires_at
                    ).toLocaleDateString()}`
                  : "Expired"}
              </Text>
            )}
          </View>
        )}

        <View style={styles.plansContainer}>
          {subscriptionPlans.map(renderPlanCard)}
        </View>

        <View style={styles.footer}>
          <Text style={styles.footerText}>
            All subscriptions include a 7-day free trial. Cancel anytime.
          </Text>
          <Text style={styles.footerText}>
            Prices may vary by region. Taxes may apply.
          </Text>
        </View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f5f5f5",
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    padding: 20,
    paddingTop: 60,
    backgroundColor: "#fff",
    borderBottomWidth: 1,
    borderBottomColor: "#e0e0e0",
  },
  backButton: {
    marginRight: 15,
  },
  backButtonText: {
    fontSize: 16,
    color: "#007AFF",
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: "bold",
    color: "#333",
  },
  centerContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  loadingText: {
    fontSize: 16,
    color: "#666",
  },
  content: {
    padding: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: "bold",
    color: "#333",
    textAlign: "center",
    marginBottom: 10,
  },
  subtitle: {
    fontSize: 16,
    color: "#666",
    textAlign: "center",
    marginBottom: 30,
  },
  currentStatus: {
    backgroundColor: "#fff",
    borderRadius: 12,
    padding: 20,
    marginBottom: 20,
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  statusTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#333",
    marginBottom: 5,
  },
  statusText: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#007AFF",
    marginBottom: 5,
  },
  expiryText: {
    fontSize: 14,
    color: "#666",
  },
  plansContainer: {
    marginBottom: 30,
  },
  planCard: {
    backgroundColor: "#fff",
    borderRadius: 16,
    padding: 24,
    marginBottom: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
    position: "relative",
  },
  popularPlan: {
    borderWidth: 2,
    borderColor: "#007AFF",
  },
  currentPlan: {
    borderWidth: 2,
    borderColor: "#34C759",
  },
  popularBadge: {
    position: "absolute",
    top: -10,
    left: 20,
    backgroundColor: "#007AFF",
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 12,
  },
  popularBadgeText: {
    color: "#fff",
    fontSize: 12,
    fontWeight: "bold",
  },
  planName: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#333",
    textAlign: "center",
    marginBottom: 10,
  },
  priceContainer: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "baseline",
    marginBottom: 20,
  },
  planPrice: {
    fontSize: 36,
    fontWeight: "bold",
    color: "#007AFF",
  },
  planPeriod: {
    fontSize: 16,
    color: "#666",
    marginLeft: 4,
  },
  featuresContainer: {
    marginBottom: 24,
  },
  featureItem: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 8,
  },
  featureCheck: {
    fontSize: 16,
    color: "#34C759",
    marginRight: 12,
    fontWeight: "bold",
  },
  featureText: {
    fontSize: 16,
    color: "#333",
    flex: 1,
  },
  subscribeButton: {
    backgroundColor: "#007AFF",
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: "center",
  },
  currentPlanButton: {
    backgroundColor: "#f0f0f0",
  },
  subscribeButtonText: {
    color: "#fff",
    fontSize: 18,
    fontWeight: "bold",
  },
  currentPlanButtonText: {
    color: "#333",
  },
  footer: {
    alignItems: "center",
    paddingVertical: 20,
  },
  footerText: {
    fontSize: 14,
    color: "#666",
    textAlign: "center",
    marginBottom: 5,
  },
});
