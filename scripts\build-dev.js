#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Building development build for notifications...\n');

// Check if EAS CLI is installed
try {
  execSync('eas --version', { stdio: 'ignore' });
} catch (error) {
  console.log('📦 Installing EAS CLI...');
  execSync('npm install -g @expo/eas-cli', { stdio: 'inherit' });
}

// Check if user is logged in to EAS
try {
  execSync('eas whoami', { stdio: 'ignore' });
} catch (error) {
  console.log('🔐 Please log in to EAS:');
  execSync('eas login', { stdio: 'inherit' });
}

// Create eas.json if it doesn't exist
const easJsonPath = path.join(process.cwd(), 'eas.json');
if (!fs.existsSync(easJsonPath)) {
  console.log('📝 Creating eas.json configuration...');
  const easConfig = {
    cli: {
      version: ">= 12.0.0"
    },
    build: {
      development: {
        developmentClient: true,
        distribution: "internal",
        ios: {
          resourceClass: "m-medium"
        },
        android: {
          buildType: "apk"
        }
      },
      preview: {
        distribution: "internal",
        ios: {
          resourceClass: "m-medium"
        },
        android: {
          buildType: "apk"
        }
      },
      production: {
        ios: {
          resourceClass: "m-medium"
        }
      }
    },
    submit: {
      production: {}
    }
  };
  
  fs.writeFileSync(easJsonPath, JSON.stringify(easConfig, null, 2));
  console.log('✅ Created eas.json');
}

console.log('\n🔨 Building development build...');
console.log('This will create a development build that supports full notifications functionality.');
console.log('\nChoose your platform:');
console.log('1. Android (APK)');
console.log('2. iOS (requires Apple Developer account)');
console.log('3. Both platforms');

// For now, let's default to Android for simplicity
console.log('\nBuilding for Android...');
try {
  execSync('eas build --platform android --profile development', { stdio: 'inherit' });
  console.log('\n✅ Development build completed!');
  console.log('\n📱 Install the APK on your device and use it instead of Expo Go');
  console.log('   to get full notifications functionality.');
} catch (error) {
  console.error('❌ Build failed:', error.message);
  console.log('\n💡 Alternative: You can also run locally with:');
  console.log('   npx expo run:android --device');
  console.log('   (requires Android Studio and a connected device)');
}
