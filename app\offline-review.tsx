import { OfflineFlashcard, offlineService } from "@/lib/offline";
import { supabase } from "@/lib/supabase";
import { useUser } from "@clerk/clerk-expo";
import { router } from "expo-router";
import React, { useEffect, useState } from "react";
import {
  Alert,
  Animated,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";

export default function OfflineReviewScreen() {
  const { user } = useUser();
  const [flashcards, setFlashcards] = useState<OfflineFlashcard[]>([]);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isFlipped, setIsFlipped] = useState(false);
  const [loading, setLoading] = useState(true);
  const [isOffline, setIsOffline] = useState(false);
  const [sessionStats, setSessionStats] = useState({
    correct: 0,
    incorrect: 0,
    total: 0,
  });
  const [flipAnimation] = useState(new Animated.Value(0));

  useEffect(() => {
    if (user) {
      initializeOfflineReview();
    }
  }, [user]);

  const initializeOfflineReview = async () => {
    try {
      // Initialize offline service
      await offlineService.initialize();

      // Check if we're offline
      const isConnected = offlineService.isConnected();
      setIsOffline(!isConnected);

      let dueCards: OfflineFlashcard[] = [];

      if (isConnected) {
        // Online: Load from database and cache
        const { data: supabaseUser } = await supabase
          .from("users")
          .select("id")
          .eq("clerk_user_id", user?.id)
          .single();

        if (supabaseUser) {
          const { data, error } = await supabase
            .from("flashcards")
            .select("*")
            .eq("user_id", supabaseUser.id)
            .lte("next_review_date", new Date().toISOString())
            .order("next_review_date", { ascending: true });

          if (!error && data) {
            dueCards = data;
            // Cache for offline use
            await offlineService.cacheFlashcards(data);
          }
        }
      } else {
        // Offline: Load from cache
        dueCards = await offlineService.getDueFlashcards();
      }

      if (dueCards.length === 0) {
        Alert.alert(
          "No Cards Due",
          isConnected
            ? "You have no flashcards due for review right now. Great job!"
            : "No cached flashcards available for offline review. Please connect to the internet to download your flashcards.",
          [{ text: "OK", onPress: () => router.back() }]
        );
        return;
      }

      setFlashcards(dueCards);
      setSessionStats((prev) => ({ ...prev, total: dueCards.length }));
    } catch (error) {
      console.error("Error initializing offline review:", error);
      Alert.alert("Error", "Failed to load flashcards");
      router.back();
    } finally {
      setLoading(false);
    }
  };

  const flipCard = () => {
    Animated.timing(flipAnimation, {
      toValue: isFlipped ? 0 : 1,
      duration: 300,
      useNativeDriver: true,
    }).start();
    setIsFlipped(!isFlipped);
  };

  const calculateNextReview = (
    difficulty: "easy" | "medium" | "hard",
    currentEaseFactor: number,
    currentInterval: number,
    reviewCount: number
  ) => {
    let newEaseFactor = currentEaseFactor;
    let newInterval = currentInterval;

    // Adjust ease factor based on difficulty
    switch (difficulty) {
      case "easy":
        newEaseFactor = Math.max(1.3, currentEaseFactor + 0.15);
        break;
      case "medium":
        newEaseFactor = Math.max(1.3, currentEaseFactor - 0.08);
        break;
      case "hard":
        newEaseFactor = Math.max(1.3, currentEaseFactor - 0.2);
        newInterval = 1; // Reset interval for hard cards
        break;
    }

    // Calculate new interval
    if (reviewCount === 0) {
      newInterval = 1;
    } else if (reviewCount === 1) {
      newInterval = 6;
    } else {
      newInterval = Math.round(newInterval * newEaseFactor);
    }

    const nextReviewDate = new Date();
    nextReviewDate.setDate(nextReviewDate.getDate() + newInterval);

    return {
      easeFactor: newEaseFactor,
      interval: newInterval,
      nextReviewDate: nextReviewDate.toISOString(),
    };
  };

  const handleAnswer = async (difficulty: "easy" | "medium" | "hard") => {
    const currentCard = flashcards[currentIndex];
    if (!currentCard) return;

    try {
      const { easeFactor, interval, nextReviewDate } = calculateNextReview(
        difficulty,
        currentCard.ease_factor,
        currentCard.interval_days,
        currentCard.review_count
      );

      // Update flashcard (works offline)
      await offlineService.updateFlashcard(currentCard.id, {
        ease_factor: easeFactor,
        interval_days: interval,
        next_review_date: nextReviewDate,
        review_count: currentCard.review_count + 1,
      });

      // Update local state
      const updatedCards = [...flashcards];
      updatedCards[currentIndex] = {
        ...currentCard,
        ease_factor: easeFactor,
        interval_days: interval,
        next_review_date: nextReviewDate,
        review_count: currentCard.review_count + 1,
      };
      setFlashcards(updatedCards);

      // Update session stats
      setSessionStats((prev) => ({
        ...prev,
        correct: difficulty === "easy" ? prev.correct + 1 : prev.correct,
        incorrect: difficulty === "hard" ? prev.incorrect + 1 : prev.incorrect,
      }));

      // Move to next card or finish session
      if (currentIndex < flashcards.length - 1) {
        setCurrentIndex(currentIndex + 1);
        setIsFlipped(false);
        flipAnimation.setValue(0);
      } else {
        finishSession();
      }
    } catch (error) {
      console.error("Error handling answer:", error);
      Alert.alert("Error", "Failed to update flashcard");
    }
  };

  const finishSession = async () => {
    try {
      const { data: supabaseUser } = await supabase
        .from("users")
        .select("id")
        .eq("clerk_user_id", user?.id)
        .single();

      if (supabaseUser) {
        // Record study session (works offline)
        await offlineService.recordStudySession({
          user_id: supabaseUser.id,
          session_type: "flashcard_review",
          duration_minutes: 10, // Estimate based on cards reviewed
          completed_at: new Date().toISOString(),
          performance_score: Math.round(
            (sessionStats.correct / sessionStats.total) * 100
          ),
        });
      }

      const accuracy = Math.round(
        (sessionStats.correct / sessionStats.total) * 100
      );

      const offlineMessage = isOffline
        ? "\n\n📱 Your progress has been saved offline and will sync when you're back online."
        : "";

      Alert.alert(
        "Session Complete!",
        `You reviewed ${sessionStats.total} cards with ${accuracy}% accuracy.\n\nCorrect: ${sessionStats.correct}\nNeed more practice: ${sessionStats.incorrect}${offlineMessage}`,
        [
          { text: "Review More", onPress: () => initializeOfflineReview() },
          { text: "Done", onPress: () => router.back() },
        ]
      );
    } catch (error) {
      console.error("Error finishing session:", error);
      router.back();
    }
  };

  if (loading) {
    return (
      <View style={styles.centerContainer}>
        <Text style={styles.loadingText}>Loading flashcards...</Text>
        {isOffline && <Text style={styles.offlineText}>📱 Offline Mode</Text>}
      </View>
    );
  }

  if (flashcards.length === 0) {
    return (
      <View style={styles.centerContainer}>
        <Text style={styles.emptyText}>No flashcards to review!</Text>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <Text style={styles.backButtonText}>Go Back</Text>
        </TouchableOpacity>
      </View>
    );
  }

  const currentCard = flashcards[currentIndex];
  const progress = ((currentIndex + 1) / flashcards.length) * 100;

  const frontInterpolate = flipAnimation.interpolate({
    inputRange: [0, 1],
    outputRange: ["0deg", "180deg"],
  });

  const backInterpolate = flipAnimation.interpolate({
    inputRange: [0, 1],
    outputRange: ["180deg", "360deg"],
  });

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.closeButton}
          onPress={() => router.back()}
        >
          <Text style={styles.closeButtonText}>✕</Text>
        </TouchableOpacity>
        <View style={styles.headerCenter}>
          <Text style={styles.progressText}>
            {currentIndex + 1} of {flashcards.length}
          </Text>
          {isOffline && <Text style={styles.offlineIndicator}>📱 Offline</Text>}
        </View>
        <View style={styles.placeholder} />
      </View>

      <View style={styles.progressBar}>
        <View style={[styles.progressFill, { width: `${progress}%` }]} />
      </View>

      <View style={styles.cardContainer}>
        <TouchableOpacity style={styles.card} onPress={flipCard}>
          <Animated.View
            style={[
              styles.cardFace,
              styles.cardFront,
              { transform: [{ rotateY: frontInterpolate }] },
            ]}
          >
            <Text style={styles.cardText}>{currentCard.front}</Text>
            <Text style={styles.tapHint}>Tap to reveal answer</Text>
          </Animated.View>

          <Animated.View
            style={[
              styles.cardFace,
              styles.cardBack,
              { transform: [{ rotateY: backInterpolate }] },
            ]}
          >
            <Text style={styles.cardText}>{currentCard.back}</Text>
          </Animated.View>
        </TouchableOpacity>

        {currentCard.category && (
          <Text style={styles.category}>{currentCard.category}</Text>
        )}
      </View>

      {isFlipped && (
        <View style={styles.answerButtons}>
          <TouchableOpacity
            style={[styles.answerButton, styles.hardButton]}
            onPress={() => handleAnswer("hard")}
          >
            <Text style={styles.answerButtonText}>Hard</Text>
            <Text style={styles.answerButtonSubtext}>Show again soon</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.answerButton, styles.mediumButton]}
            onPress={() => handleAnswer("medium")}
          >
            <Text style={styles.answerButtonText}>Good</Text>
            <Text style={styles.answerButtonSubtext}>Show in a few days</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.answerButton, styles.easyButton]}
            onPress={() => handleAnswer("easy")}
          >
            <Text style={styles.answerButtonText}>Easy</Text>
            <Text style={styles.answerButtonSubtext}>Show much later</Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f5f5f5",
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 20,
    paddingTop: 60,
    backgroundColor: "#fff",
  },
  closeButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: "#f0f0f0",
    justifyContent: "center",
    alignItems: "center",
  },
  closeButtonText: {
    fontSize: 16,
    color: "#666",
  },
  headerCenter: {
    alignItems: "center",
  },
  progressText: {
    fontSize: 16,
    fontWeight: "600",
    color: "#333",
  },
  offlineIndicator: {
    fontSize: 12,
    color: "#FF9500",
    marginTop: 2,
  },
  placeholder: {
    width: 32,
  },
  progressBar: {
    height: 4,
    backgroundColor: "#e0e0e0",
    marginHorizontal: 20,
  },
  progressFill: {
    height: "100%",
    backgroundColor: "#007AFF",
  },
  centerContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
  loadingText: {
    fontSize: 16,
    color: "#666",
    marginBottom: 10,
  },
  offlineText: {
    fontSize: 14,
    color: "#FF9500",
  },
  emptyText: {
    fontSize: 18,
    color: "#666",
    marginBottom: 20,
    textAlign: "center",
  },
  backButton: {
    backgroundColor: "#007AFF",
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
  },
  backButtonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
  },
  cardContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
  card: {
    width: "100%",
    height: 300,
    marginBottom: 20,
  },
  cardFace: {
    position: "absolute",
    width: "100%",
    height: "100%",
    backgroundColor: "#fff",
    borderRadius: 16,
    padding: 30,
    justifyContent: "center",
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
    backfaceVisibility: "hidden",
  },
  cardFront: {
    backgroundColor: "#fff",
  },
  cardBack: {
    backgroundColor: "#f8f9fa",
  },
  cardText: {
    fontSize: 20,
    color: "#333",
    textAlign: "center",
    lineHeight: 28,
    marginBottom: 20,
  },
  tapHint: {
    fontSize: 14,
    color: "#999",
    fontStyle: "italic",
  },
  category: {
    fontSize: 14,
    color: "#666",
    backgroundColor: "#f0f0f0",
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 12,
  },
  answerButtons: {
    flexDirection: "row",
    justifyContent: "space-between",
    padding: 20,
    paddingBottom: 40,
  },
  answerButton: {
    flex: 1,
    marginHorizontal: 5,
    paddingVertical: 15,
    borderRadius: 12,
    alignItems: "center",
  },
  hardButton: {
    backgroundColor: "#FF3B30",
  },
  mediumButton: {
    backgroundColor: "#FF9500",
  },
  easyButton: {
    backgroundColor: "#34C759",
  },
  answerButtonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 4,
  },
  answerButtonSubtext: {
    color: "#fff",
    fontSize: 12,
    opacity: 0.9,
    textAlign: "center",
  },
});
